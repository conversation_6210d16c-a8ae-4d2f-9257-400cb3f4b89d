<script setup lang="ts">
import type { VxeTablePropTypes } from 'vxe-table';

import { ref } from 'vue';

import { Button, InputNumber } from 'ant-design-vue';

import { DrugSelect } from '#/components/clinic/drug-select';

interface Medicine {
  id: string;
  name: string;
  weight: number;
  price: number;
}

const tableData = ref<Medicine[]>([
  {
    id: '1',
    name: '感冒药',
    weight: 100,
    price: 9.9,
  },
]);

const editConfig = ref<VxeTablePropTypes.EditConfig>({
  trigger: 'click',
  mode: 'cell',
  showStatus: true,
});

const addRow = () => {
  tableData.value.push({
    id: Date.now().toString(),
    name: '',
    weight: 0,
    price: 0,
  });
};

const removeRow = (row: Medicine) => {
  const index = tableData.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    tableData.value.splice(index, 1);
  }
};

// 暴露方法给父组件
defineExpose({
  addRow,
});
</script>

<template>
  <div>
    <vxe-table
      :data="tableData"
      :edit-config="editConfig"
      border
      stripe
      size="small"
    >
      <vxe-column type="seq" width="60" title="序号" />
      <vxe-column field="name" title="药品名称" :edit-render="{}">
        <template #edit="{ row }">
          <DrugSelect v-model:value="row.name" class="w-full" />
        </template>
      </vxe-column>
      <vxe-column field="weight" title="数量" :edit-render="{}">
        <template #edit="{ row }">
          <InputNumber
            v-model:value="row.weight"
            type="number"
            placeholder="请输入克数"
            :min="0"
          >
            <template #addonAfter>
              <Select>
                <SelectOption value="g">g</SelectOption>
                <SelectOption value="kg">kg</SelectOption>
                <SelectOption value="3">袋</SelectOption>
              </Select>
            </template>
          </InputNumber>
        </template>
      </vxe-column>
      <vxe-column field="price" title="价格(元)" />
      <vxe-column title="操作" width="100" fixed="right">
        <template #default="{ row }">
          <Button
            type="link"
            danger
            size="small"
            class="flex items-center"
            @click="removeRow(row)"
          >
            <i
              class="icon-[material-symbols-light--delete-outline-rounded] mr-1"
            ></i>
            删除
          </Button>
        </template>
      </vxe-column>
    </vxe-table>
    <div class="mt-4 flex justify-end text-sm">
      <div class="text-gray-500">
        总价：<span class="text-primary">{{
          tableData.reduce((sum, item) => sum + item.price, 0).toFixed(2)
        }}</span>
        元
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.vxe-table) {
  border-radius: 8px;
}
</style>
